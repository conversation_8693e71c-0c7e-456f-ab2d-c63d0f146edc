<?php

namespace App\Controller;

use App\Entity\Product;
use Doctrine\ORM\EntityManagerInterface;
use S<PERSON>fony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/product', name: 'product')]
final class ProductController extends AbstractController
{
    #[Route('/create', name: '_create', methods:['POST'])]
    public function create(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try{
            // $data = json_decode($request->getContent(),true);
            $data = ['name' => 'Product 1', 'price' => 100.12, 'quantity' => 100];
            $product = new Product();
            $product->setName($data['name']);
            $product->setPrice($data['price']);
            $product->setQuantity($data['quantity']);   
            $em->persist($product);
            $em->flush();
            return new JsonResponse(['message' => 'Product created']); 
        } catch(\Exception $e){
            throw $e;
        }  
    }
    
    #[Route('/{id}', '_getbyid',methods:['GET'])]
    function getbyid(Request $request,int $id, EntityManagerInterface $em): JsonResponse
    {
        try{
            $product =  $em->getRepository(Product::class)->find($id);
            return new JsonResponse($product);
        } catch(\Exception $e){
            return new JsonResponse(['message' => $e->getMessage()]);
        }
        
    }
}